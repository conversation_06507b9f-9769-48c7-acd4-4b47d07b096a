import { DragGesture } from '../../gestures/drag/index.mjs';
import { PanGesture } from '../../gestures/pan/index.mjs';
import { MeasureLayout } from './layout/MeasureLayout.mjs';
import { HTMLProjectionNode } from '../../projection/node/HTMLProjectionNode.mjs';

const drag = {
    pan: {
        Feature: PanGesture,
    },
    drag: {
        Feature: DragGesture,
        ProjectionNode: HTMLProjectionNode,
        MeasureLayout,
    },
};

export { drag };
